# B. General Requirements
    The customer operates a call center to support their end-users, utilizing Five9 as their softphone
    solution. Information is stored across various platforms, including a CMS website with PostgreSQL
    databases, SharePoint sites, Confluence pages, ServiceNow, MS Office files (PPTX, XLSX), and
    PDFs. Call records are saved as MP3 audio files on MS OneDrive after calls conclude. As a technical
    specialist, your task is to design and implement an application that allows users to request
    information through natural language interactions using an LLM-based chatbot. The application
    should leverage AI services from Azure, AWS, GCP, or OpenAI. Additionally, it should guide users
    on which departments to contact for specific processes, providing a workflow to accomplish tasks.

# C. Requirement Description
    1. Natural Language Processing (NLP):
    - Implement NLP capabilities to understand and process user queries.
    - Use AI services from Azure, AWS, GCP, or OpenAI for NLP tasks.
    2. Data Integration:

    - Integrate with a CMS website, PostgreSQL databases
    MS Office files (PPTX, XLSX), PDFs, and MP3 audio files to retrieve information.
    - Ensure the application can handle different data formats and extract relevant information.
    3. Information Retrieval:
    - Develop a mechanism to search and retrieve information based on user queries for the
    chatbot.
    - Implement a ranking system to prioritize the most relevant information (Optional).
    - Provide users with information about the departments they should work with for specific
    processes (a simple workflow to guide users through the necessary steps to get things
    done) when users ask about them.
    4. Scalability and Performance:
    - Design the application to handle multiple concurrent users efficiently (Optional).
    - Optimize performance to ensure quick response times.
    5. Security:
    - Design security measures to protect sensitive information.
    - Ensure compliance with relevant data protection regulations.
    6. Documentation:
    - Provide thorough answers to the questions in the "General Questions" section.
    - Present a high-level design of the application, detailing the system components and their
    interactions.
    - Provide a rationale for the selected technologies and services.

# D. Deliverables:
    1. Demo:
    - Provide a functional demo of the application. Ensure the application operates with at least
    a CMS website (you may create your own) and MS Office file samples (you can choose
    any storage types).
    - Demonstrate the key features and functionalities.
    2. Presentation:
    - Prepare a presentation summarizing the project, including challenges faced and solutions
    implemented as mentioned in requirements’ documentation section.